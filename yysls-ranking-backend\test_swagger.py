#!/usr/bin/env python3
"""
测试Swagger文档是否正常显示
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from app.main import app
    print("✅ 应用导入成功")
    
    # 获取OpenAPI schema
    openapi_schema = app.openapi()
    print(f"✅ OpenAPI schema生成成功")
    
    # 检查路径数量
    paths = openapi_schema.get('paths', {})
    print(f"📊 API端点数量: {len(paths)}")
    
    # 按标签分组显示
    tags_count = {}
    for path, methods in paths.items():
        for method, details in methods.items():
            if method.upper() in ['GET', 'POST', 'PUT', 'DELETE', 'PATCH']:
                tags = details.get('tags', ['未分类'])
                tag = tags[0] if tags else '未分类'
                if tag not in tags_count:
                    tags_count[tag] = 0
                tags_count[tag] += 1
    
    print("\n📋 API模块统计:")
    for tag, count in tags_count.items():
        print(f"  - {tag}: {count}个端点")
    
    # 检查系统配置相关端点
    system_config_paths = [path for path in paths.keys() if '/system' in path]
    print(f"\n⚙️ 系统配置端点数量: {len(system_config_paths)}")
    for path in system_config_paths:
        print(f"  - {path}")
    
    # 检查内容管理相关端点
    content_paths = [path for path in paths.keys() if '/content' in path]
    print(f"\n📝 内容管理端点数量: {len(content_paths)}")
    for path in content_paths:
        print(f"  - {path}")
    
    print(f"\n🎉 测试完成！现在可以访问 http://localhost:8000/docs 查看Swagger文档")
    
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("请确保已安装所有依赖")
    sys.exit(1)
except Exception as e:
    print(f"❌ 测试失败: {e}")
    sys.exit(1)
