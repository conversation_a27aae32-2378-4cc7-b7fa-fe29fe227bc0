#!/usr/bin/env python3
"""
测试模块导入是否正常
"""
import sys
import os

# 确保当前目录在Python路径中
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

print("Python路径:")
for path in sys.path:
    print(f"  {path}")

print("\n测试模块导入:")

try:
    from app.config import settings
    print("✓ app.config 导入成功")
except ImportError as e:
    print(f"✗ app.config 导入失败: {e}")

try:
    import logging
    print("✓ logging 模块导入成功")
except ImportError as e:
    print(f"✗ logging 模块导入失败: {e}")

try:
    from app.core.database import get_db
    print("✓ app.core.database 导入成功")
except ImportError as e:
    print(f"✗ app.core.database 导入失败: {e}")

try:
    from app.core.auth import get_current_user
    print("✓ app.core.auth 导入成功")
except ImportError as e:
    print(f"✗ app.core.auth 导入失败: {e}")

try:
    from app.services.user_service import UserService
    print("✓ app.services.user_service 导入成功")
except ImportError as e:
    print(f"✗ app.services.user_service 导入失败: {e}")

try:
    from app.main import app
    print("✓ app.main 导入成功")
except ImportError as e:
    print(f"✗ app.main 导入失败: {e}")

print("\n模块导入测试完成!")
